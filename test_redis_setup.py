#!/usr/bin/env python3
"""
Quick Redis Setup Test

This script tests the Redis connection and basic functionality
with your AWS MemoryDB instance.
"""

import asyncio
import os
import sys
from datetime import datetime, timezone
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.infrastructure.redis_service_registry import RedisServiceRegistry
from core.infrastructure.cache import RedisCache


async def test_basic_redis_connection():
    """Test basic Redis connectivity"""
    redis_url = os.getenv("REDIS_URL", "redis://redis-10798.c292.ap-southeast-1-1.ec2.redns.redis-cloud.com:10798")

    print("🔍 Testing Basic Redis Connection")
    print("=" * 50)
    print(f"Redis URL: {redis_url}")
    print()

    try:
        import redis.asyncio as aioredis

        # Test connection
        redis_client = await aioredis.from_url(
            redis_url,
            encoding="utf-8",
            decode_responses=True,
            socket_timeout=10,
            socket_connect_timeout=10
        )

        # Test ping
        print("1. Testing ping...")
        pong = await redis_client.ping()
        print(f"   ✅ Ping successful: {pong}")

        # Test basic operations
        print("2. Testing basic operations...")
        test_key = f"test_connection_{int(datetime.now().timestamp())}"
        test_value = "redis_working"

        await redis_client.set(test_key, test_value, ex=30)
        retrieved_value = await redis_client.get(test_key)

        if retrieved_value == test_value:
            print(f"   ✅ Set/Get operation successful")
        else:
            print(f"   ❌ Set/Get operation failed: expected '{test_value}', got '{retrieved_value}'")
            return False

        # Test TTL
        print("3. Testing TTL...")
        ttl = await redis_client.ttl(test_key)
        print(f"   ✅ TTL check successful: {ttl} seconds remaining")

        # Test delete
        print("4. Testing delete...")
        await redis_client.delete(test_key)
        exists = await redis_client.exists(test_key)
        if exists == 0:
            print(f"   ✅ Delete operation successful")
        else:
            print(f"   ❌ Delete operation failed: key still exists")

        # Test info
        print("5. Testing server info...")
        info = await redis_client.info("server")
        redis_version = info.get("redis_version", "unknown")
        print(f"   ✅ Redis version: {redis_version}")

        await redis_client.close()
        print("\n🎉 Basic Redis connection test passed!")
        return True

    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False


async def test_redis_cache():
    """Test Redis cache functionality"""
    redis_url = os.getenv("REDIS_URL", "redis://redis-10798.c292.ap-southeast-1-1.ec2.redns.redis-cloud.com:10798")

    print("\n🗄️  Testing Redis Cache")
    print("=" * 50)

    try:
        cache = RedisCache(redis_url=redis_url, key_prefix="test_cache:")

        # Test cache operations
        print("1. Testing cache set/get...")
        test_data = {
            "message": "Hello Redis!",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "number": 42,
            "list": [1, 2, 3, 4, 5]
        }

        await cache.set("test_data", test_data, ttl=60)
        retrieved_data = await cache.get("test_data")

        if retrieved_data == test_data:
            print("   ✅ Cache set/get successful")
        else:
            print(f"   ❌ Cache set/get failed")
            return False

        # Test cache exists
        print("2. Testing cache exists...")
        exists = await cache.exists("test_data")
        if exists:
            print("   ✅ Cache exists check successful")
        else:
            print("   ❌ Cache exists check failed")

        # Test cache delete
        print("3. Testing cache delete...")
        await cache.delete("test_data")
        exists_after_delete = await cache.exists("test_data")
        if not exists_after_delete:
            print("   ✅ Cache delete successful")
        else:
            print("   ❌ Cache delete failed")

        print("\n🎉 Redis cache test passed!")
        return True

    except Exception as e:
        print(f"❌ Redis cache test failed: {e}")
        return False


async def test_service_registry():
    """Test Redis service registry functionality"""
    redis_url = os.getenv("REDIS_URL", "redis://redis-10798.c292.ap-southeast-1-1.ec2.redns.redis-cloud.com:10798")

    print("\n🏢 Testing Redis Service Registry")
    print("=" * 50)

    try:
        registry = RedisServiceRegistry(
            redis_url=redis_url,
            key_prefix="test_registry:",
            default_ttl=120,
            health_check_interval=30
        )

        # Test service registration
        print("1. Testing service registration...")
        service_name = f"test-service-{int(datetime.now().timestamp())}"
        result = await registry.register_service(
            name=service_name,
            url="http://localhost:8001",
            version="1.0.0",
            metadata={"test": True, "environment": "testing"}
        )

        if result["success"]:
            print(f"   ✅ Service registration successful")
            print(f"      Service: {service_name}")
            print(f"      API Key: {result['api_key'][:20]}...")
        else:
            print(f"   ❌ Service registration failed: {result['error']}")
            return False

        # Test service lookup
        print("2. Testing service lookup...")
        service = await registry.get_service(service_name)
        if service and service["name"] == service_name:
            print(f"   ✅ Service lookup successful")
            print(f"      URL: {service['url']}")
            print(f"      Version: {service['version']}")
        else:
            print(f"   ❌ Service lookup failed")
            return False

        # Test heartbeat
        print("3. Testing heartbeat...")
        heartbeat_result = await registry.heartbeat(service_name)
        if heartbeat_result["success"]:
            print(f"   ✅ Heartbeat successful")
        else:
            print(f"   ❌ Heartbeat failed: {heartbeat_result['error']}")

        # Test service listing
        print("4. Testing service listing...")
        services = await registry.list_services()
        service_names = [s["name"] for s in services]
        if service_name in service_names:
            print(f"   ✅ Service listing successful ({len(services)} services found)")
        else:
            print(f"   ❌ Service listing failed")

        # Test service stats
        print("5. Testing service statistics...")
        stats = await registry.get_service_stats()
        print(f"   ✅ Service stats: {stats['total_services']} total, {stats['healthy_services']} healthy")

        # Cleanup
        print("6. Cleaning up...")
        await registry.unregister_service(service_name)
        await registry.dispose()
        print("   ✅ Cleanup successful")

        print("\n🎉 Redis service registry test passed!")
        return True

    except Exception as e:
        print(f"❌ Redis service registry test failed: {e}")
        return False


async def test_performance():
    """Test Redis performance"""
    redis_url = os.getenv("REDIS_URL", "redis://redis-10798.c292.ap-southeast-1-1.ec2.redns.redis-cloud.com:10798")

    print("\n⚡ Testing Redis Performance")
    print("=" * 50)

    try:
        import redis.asyncio as aioredis
        import time

        redis_client = await aioredis.from_url(redis_url)

        # Performance test: 100 concurrent operations
        print("1. Testing 100 concurrent set operations...")
        start_time = time.time()

        async def set_operation(i: int):
            await redis_client.set(f"perf_test_{i}", f"value_{i}", ex=60)

        await asyncio.gather(*[set_operation(i) for i in range(100)])
        set_time = time.time() - start_time
        print(f"   ✅ 100 set operations completed in {set_time:.2f} seconds")

        # Performance test: 100 concurrent get operations
        print("2. Testing 100 concurrent get operations...")
        start_time = time.time()

        async def get_operation(i: int):
            return await redis_client.get(f"perf_test_{i}")

        results = await asyncio.gather(*[get_operation(i) for i in range(100)])
        get_time = time.time() - start_time
        print(f"   ✅ 100 get operations completed in {get_time:.2f} seconds")

        # Verify results
        success_count = sum(1 for i, result in enumerate(results) if result == f"value_{i}".encode())
        print(f"   ✅ {success_count}/100 operations returned correct values")

        # Cleanup
        await redis_client.delete(*[f"perf_test_{i}" for i in range(100)])
        await redis_client.close()

        print(f"\n🎉 Performance test completed!")
        print(f"   Average set time: {(set_time/100)*1000:.2f}ms per operation")
        print(f"   Average get time: {(get_time/100)*1000:.2f}ms per operation")

        return True

    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False


async def main():
    """Run all Redis tests"""
    print("🚀 Redis Setup and Testing Suite")
    print("=" * 60)
    print(f"Timestamp: {datetime.now(timezone.utc).isoformat()}")
    print()

    tests = [
        ("Basic Connection", test_basic_redis_connection),
        ("Cache Functionality", test_redis_cache),
        ("Service Registry", test_service_registry),
        ("Performance", test_performance)
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False

    # Summary
    print("\n📊 Test Summary")
    print("=" * 60)
    passed = sum(1 for result in results.values() if result)
    total = len(results)

    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 All Redis tests passed! Your Redis setup is working correctly.")
        print("✅ Redis connection established")
        print("✅ Cache operations working")
        print("✅ Service registry functional")
        print("✅ Performance acceptable")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the Redis configuration.")

    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
